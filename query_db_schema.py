#!/usr/bin/env python3
"""
Script to query database schema and see what tables exist
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_config import get_db_connection
import psycopg2
from psycopg2.extras import RealDictCursor

def query_table_schema():
    """Query the database to see what tables exist and their structure"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("=== DATABASE TABLES ===")
        
        # Get all tables
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name;
        """)
        tables = cursor.fetchall()
        
        print(f"Found {len(tables)} tables:")
        for table in tables:
            print(f"- {table['table_name']}")
        
        print("\n=== TABLE STRUCTURES ===")
        
        # Get structure for key tables we're interested in
        key_tables = ['wafers', 'lots', 'xfab_fr_lots', 'xfab_de_lots', 'chips']
        
        for table_name in key_tables:
            print(f"\n--- {table_name.upper()} TABLE ---")
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_schema = 'public' 
                AND table_name = %s
                ORDER BY ordinal_position;
            """, (table_name,))
            
            columns = cursor.fetchall()
            if columns:
                print(f"Columns in {table_name}:")
                for col in columns:
                    nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
                    default = f" DEFAULT {col['column_default']}" if col['column_default'] else ""
                    print(f"  {col['column_name']}: {col['data_type']} {nullable}{default}")
            else:
                print(f"Table {table_name} does not exist")
        
        # Check for any tables with 'lot' in the name
        print(f"\n=== TABLES WITH 'LOT' IN NAME ===")
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            AND table_name LIKE '%lot%'
            ORDER BY table_name;
        """)
        lot_tables = cursor.fetchall()
        
        for table in lot_tables:
            table_name = table['table_name']
            print(f"\n--- {table_name.upper()} TABLE ---")
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_schema = 'public' 
                AND table_name = %s
                ORDER BY ordinal_position;
            """, (table_name,))
            
            columns = cursor.fetchall()
            for col in columns:
                nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
                default = f" DEFAULT {col['column_default']}" if col['column_default'] else ""
                print(f"  {col['column_name']}: {col['data_type']} {nullable}{default}")
        
        # Check for any tables with 'xfab' in the name
        print(f"\n=== TABLES WITH 'XFAB' IN NAME ===")
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            AND table_name LIKE '%xfab%'
            ORDER BY table_name;
        """)
        xfab_tables = cursor.fetchall()
        
        for table in xfab_tables:
            table_name = table['table_name']
            print(f"\n--- {table_name.upper()} TABLE ---")
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_schema = 'public' 
                AND table_name = %s
                ORDER BY ordinal_position;
            """, (table_name,))
            
            columns = cursor.fetchall()
            for col in columns:
                nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
                default = f" DEFAULT {col['column_default']}" if col['column_default'] else ""
                print(f"  {col['column_name']}: {col['data_type']} {nullable}{default}")
        
        # Check for any tables with 'chip' in the name
        print(f"\n=== TABLES WITH 'CHIP' IN NAME ===")
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            AND table_name LIKE '%chip%'
            ORDER BY table_name;
        """)
        chip_tables = cursor.fetchall()
        
        for table in chip_tables:
            table_name = table['table_name']
            print(f"\n--- {table_name.upper()} TABLE ---")
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_schema = 'public' 
                AND table_name = %s
                ORDER BY ordinal_position;
            """, (table_name,))
            
            columns = cursor.fetchall()
            for col in columns:
                nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
                default = f" DEFAULT {col['column_default']}" if col['column_default'] else ""
                print(f"  {col['column_name']}: {col['data_type']} {nullable}{default}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"Error querying database: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    query_table_schema()
